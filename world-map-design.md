# 全球在线用户分布交互式世界地图可视化方案

## AI 完美提示词

### 核心需求描述
```
请帮我创建一个现代化的全球在线用户分布交互式世界地图可视化组件，具备以下特性：

**数据展示能力：**
- 实时显示全球各国家/地区的在线用户数量和密度
- 支持多种数据可视化模式：热力图、散点图、区域填充
- 动态数据更新，支持WebSocket实时推送
- 数据聚合和钻取功能（国家→省份→城市）

**视觉设计要求：**
- 采用墨卡托投影的平面世界地图，长方形布局
- 现代化深色主题配色，支持多主题切换
- 渐变色彩映射用户密度（低密度：蓝色系 → 高密度：红色系）
- 流畅的动画过渡效果和微交互
- 响应式设计，适配桌面端和移动端

**交互功能：**
- 鼠标悬停显示详细信息卡片
- 点击国家/地区进行数据钻取
- 缩放和平移操作
- 时间轴控制历史数据回放
- 数据筛选和搜索功能

**技术实现：**
- 基于ECharts 5.x + TypeScript
- 模块化组件设计，易于集成
- 高性能渲染，支持大数据量
- 完整的类型定义和API文档
```

## 技术架构设计

### 1. 核心技术栈
- **可视化引擎**: ECharts 5.x
- **开发语言**: TypeScript
- **构建工具**: Vite/Webpack
- **样式方案**: CSS-in-JS / SCSS
- **数据处理**: D3.js (可选)

### 2. 组件架构
```
WorldMapVisualization/
├── components/
│   ├── WorldMap.tsx          # 主地图组件
│   ├── DataPanel.tsx         # 数据面板
│   ├── TimelineControl.tsx   # 时间轴控制
│   ├── ThemeSelector.tsx     # 主题选择器
│   └── InfoTooltip.tsx       # 信息提示框
├── hooks/
│   ├── useMapData.ts         # 地图数据管理
│   ├── useRealTimeData.ts    # 实时数据订阅
│   └── useMapInteraction.ts  # 交互逻辑
├── utils/
│   ├── mapConfig.ts          # 地图配置
│   ├── dataProcessor.ts      # 数据处理
│   └── colorSchemes.ts       # 配色方案
└── types/
    └── index.ts              # 类型定义
```

## 核心实现方案

### 1. 地图配置 (mapConfig.ts)
```typescript
export const MAP_CONFIG = {
  // 地图投影和布局
  projection: 'mercator',
  aspectRatio: 2.1, // 长宽比
  center: [0, 20],   // 地图中心点
  
  // 视觉样式
  themes: {
    dark: {
      backgroundColor: '#0a0e1a',
      borderColor: '#1e293b',
      areaColors: {
        default: '#1e293b',
        hover: '#334155'
      },
      textColor: '#e2e8f0'
    },
    light: {
      backgroundColor: '#f8fafc',
      borderColor: '#e2e8f0',
      areaColors: {
        default: '#f1f5f9',
        hover: '#e2e8f0'
      },
      textColor: '#334155'
    }
  },
  
  // 数据映射
  dataMapping: {
    userDensity: {
      min: 0,
      max: 10000,
      colorRange: ['#3b82f6', '#8b5cf6', '#f59e0b', '#ef4444']
    }
  }
};
```

### 2. 主地图组件 (WorldMap.tsx)
```typescript
interface WorldMapProps {
  data: UserDistributionData[];
  theme?: 'dark' | 'light';
  realTimeUpdate?: boolean;
  onCountryClick?: (country: CountryData) => void;
  onDataUpdate?: (data: UserDistributionData[]) => void;
}

export const WorldMap: React.FC<WorldMapProps> = ({
  data,
  theme = 'dark',
  realTimeUpdate = true,
  onCountryClick,
  onDataUpdate
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const { mapData, loading } = useMapData();
  const { realtimeData } = useRealTimeData(realTimeUpdate);
  
  // ECharts 配置
  const option = useMemo(() => ({
    backgroundColor: MAP_CONFIG.themes[theme].backgroundColor,
    
    geo: {
      map: 'world',
      projection: {
        project: (point: [number, number]) => [
          point[0] / 180 * Math.PI,
          -Math.log(Math.tan((90 + point[1]) * Math.PI / 360))
        ],
        unproject: (point: [number, number]) => [
          point[0] * 180 / Math.PI,
          2 * 180 / Math.PI * Math.atan(Math.exp(point[1])) - 90
        ]
      },
      aspectScale: 0.75,
      roam: true,
      itemStyle: {
        areaColor: MAP_CONFIG.themes[theme].areaColors.default,
        borderColor: MAP_CONFIG.themes[theme].borderColor,
        borderWidth: 0.5
      },
      emphasis: {
        itemStyle: {
          areaColor: MAP_CONFIG.themes[theme].areaColors.hover
        }
      }
    },
    
    series: [
      // 热力图层
      {
        type: 'map',
        map: 'world',
        geoIndex: 0,
        data: processMapData(data),
        emphasis: {
          itemStyle: {
            areaColor: null
          }
        }
      },
      // 散点图层
      {
        type: 'scatter',
        coordinateSystem: 'geo',
        data: processScatterData(data),
        symbolSize: (val: number[]) => Math.sqrt(val[2]) * 2,
        itemStyle: {
          color: '#fbbf24',
          shadowBlur: 10,
          shadowColor: '#fbbf24'
        }
      }
    ],
    
    visualMap: {
      min: 0,
      max: 10000,
      text: ['高', '低'],
      realtime: false,
      calculable: true,
      inRange: {
        color: MAP_CONFIG.dataMapping.userDensity.colorRange
      },
      textStyle: {
        color: MAP_CONFIG.themes[theme].textColor
      }
    },
    
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => `
        <div class="map-tooltip">
          <h3>${params.name}</h3>
          <p>在线用户: ${params.value || 0}</p>
          <p>用户密度: ${calculateDensity(params.value)}</p>
        </div>
      `
    }
  }), [data, theme, mapData]);
  
  return (
    <div className="world-map-container">
      <div ref={chartRef} className="world-map-chart" />
      <DataPanel data={data} />
      <TimelineControl onTimeChange={handleTimeChange} />
      <ThemeSelector currentTheme={theme} onThemeChange={setTheme} />
    </div>
  );
};
```

### 3. 实时数据管理 (useRealTimeData.ts)
```typescript
export const useRealTimeData = (enabled: boolean) => {
  const [data, setData] = useState<UserDistributionData[]>([]);
  const [connected, setConnected] = useState(false);
  
  useEffect(() => {
    if (!enabled) return;
    
    const ws = new WebSocket('wss://api.example.com/realtime-users');
    
    ws.onopen = () => setConnected(true);
    ws.onmessage = (event) => {
      const newData = JSON.parse(event.data);
      setData(prevData => updateUserDistribution(prevData, newData));
    };
    ws.onclose = () => setConnected(false);
    
    return () => ws.close();
  }, [enabled]);
  
  return { realtimeData: data, connected };
};
```

## 配色方案设计

### 1. 深色主题 (推荐)
```css
:root {
  --bg-primary: #0a0e1a;
  --bg-secondary: #1e293b;
  --border-color: #334155;
  --text-primary: #e2e8f0;
  --text-secondary: #94a3b8;
  
  /* 数据密度配色 */
  --density-low: #3b82f6;
  --density-medium: #8b5cf6;
  --density-high: #f59e0b;
  --density-extreme: #ef4444;
  
  /* 交互配色 */
  --hover-color: #475569;
  --active-color: #fbbf24;
}
```

### 2. 浅色主题
```css
:root[data-theme="light"] {
  --bg-primary: #f8fafc;
  --bg-secondary: #f1f5f9;
  --border-color: #e2e8f0;
  --text-primary: #334155;
  --text-secondary: #64748b;
}
```

## 使用说明

### 1. 安装依赖
```bash
npm install echarts @types/echarts react typescript
```

### 2. 基础使用
```typescript
import { WorldMap } from './components/WorldMap';

const App = () => {
  const userData = [
    { country: 'China', value: 8500, coordinates: [104.195, 35.861] },
    { country: 'United States', value: 6200, coordinates: [-95.712, 37.090] },
    // ... 更多数据
  ];
  
  return (
    <WorldMap
      data={userData}
      theme="dark"
      realTimeUpdate={true}
      onCountryClick={(country) => console.log('Clicked:', country)}
    />
  );
};
```

### 3. 高级配置
```typescript
// 自定义配色方案
const customTheme = {
  backgroundColor: '#1a1a2e',
  dataColors: ['#16213e', '#0f3460', '#533483', '#e94560'],
  textColor: '#eee'
};

// 自定义数据处理
const customDataProcessor = (rawData: any[]) => {
  return rawData.map(item => ({
    name: item.countryName,
    value: item.activeUsers,
    density: item.userDensity,
    coordinates: [item.longitude, item.latitude]
  }));
};
```

## 性能优化建议

1. **数据分层加载**: 根据缩放级别动态加载不同精度的数据
2. **虚拟化渲染**: 大数据量时使用数据采样和聚合
3. **缓存策略**: 实现地图瓦片和数据的本地缓存
4. **懒加载**: 按需加载地图资源和组件
5. **防抖优化**: 对实时数据更新进行防抖处理

## 扩展功能

1. **多维度数据展示**: 支持按时间、设备类型、用户类型等维度筛选
2. **数据导出**: 支持导出图表为图片或PDF
3. **自定义标记**: 允许用户添加自定义地理标记
4. **数据对比**: 支持多时间段数据对比显示
5. **告警系统**: 异常数据变化时的视觉告警

## 完整代码示例

### 1. 数据类型定义 (types/index.ts)
```typescript
export interface UserDistributionData {
  country: string;
  countryCode: string;
  value: number;
  coordinates: [number, number];
  density?: number;
  growth?: number;
  timestamp?: number;
}

export interface CountryData {
  name: string;
  code: string;
  users: number;
  density: number;
  regions?: RegionData[];
}

export interface RegionData {
  name: string;
  users: number;
  coordinates: [number, number];
}

export interface MapTheme {
  backgroundColor: string;
  borderColor: string;
  areaColors: {
    default: string;
    hover: string;
  };
  textColor: string;
  dataColors: string[];
}
```

### 2. 数据处理工具 (utils/dataProcessor.ts)
```typescript
export const processMapData = (data: UserDistributionData[]) => {
  return data.map(item => ({
    name: item.country,
    value: item.value,
    itemStyle: {
      areaColor: getColorByValue(item.value),
      borderColor: '#334155',
      borderWidth: 0.5
    }
  }));
};

export const processScatterData = (data: UserDistributionData[]) => {
  return data
    .filter(item => item.coordinates && item.value > 100)
    .map(item => ({
      name: item.country,
      value: [...item.coordinates, item.value],
      symbolSize: Math.min(Math.sqrt(item.value / 10), 50),
      itemStyle: {
        color: getHeatColor(item.density || 0),
        shadowBlur: 15,
        shadowColor: getHeatColor(item.density || 0)
      }
    }));
};

export const getColorByValue = (value: number): string => {
  const colors = ['#3b82f6', '#8b5cf6', '#f59e0b', '#ef4444'];
  const thresholds = [100, 1000, 5000, 10000];

  for (let i = 0; i < thresholds.length; i++) {
    if (value <= thresholds[i]) {
      return colors[i];
    }
  }
  return colors[colors.length - 1];
};

export const getHeatColor = (density: number): string => {
  const ratio = Math.min(density / 100, 1);
  const r = Math.floor(255 * ratio);
  const g = Math.floor(255 * (1 - ratio));
  const b = 100;
  return `rgba(${r}, ${g}, ${b}, 0.8)`;
};
```

### 3. 交互控制组件 (components/DataPanel.tsx)
```typescript
interface DataPanelProps {
  data: UserDistributionData[];
  onFilterChange?: (filters: FilterOptions) => void;
}

export const DataPanel: React.FC<DataPanelProps> = ({ data, onFilterChange }) => {
  const [filters, setFilters] = useState<FilterOptions>({
    minUsers: 0,
    maxUsers: Infinity,
    countries: [],
    timeRange: 'all'
  });

  const totalUsers = useMemo(() =>
    data.reduce((sum, item) => sum + item.value, 0), [data]
  );

  const topCountries = useMemo(() =>
    data.sort((a, b) => b.value - a.value).slice(0, 10), [data]
  );

  return (
    <div className="data-panel">
      <div className="stats-summary">
        <h3>全球统计</h3>
        <div className="stat-item">
          <span className="stat-label">总在线用户</span>
          <span className="stat-value">{totalUsers.toLocaleString()}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">活跃国家</span>
          <span className="stat-value">{data.length}</span>
        </div>
      </div>

      <div className="top-countries">
        <h4>用户最多的国家</h4>
        {topCountries.map((country, index) => (
          <div key={country.countryCode} className="country-item">
            <span className="rank">#{index + 1}</span>
            <span className="country-name">{country.country}</span>
            <span className="user-count">{country.value.toLocaleString()}</span>
          </div>
        ))}
      </div>

      <div className="filters">
        <h4>数据筛选</h4>
        <div className="filter-group">
          <label>最小用户数</label>
          <input
            type="range"
            min="0"
            max="10000"
            value={filters.minUsers}
            onChange={(e) => {
              const newFilters = { ...filters, minUsers: Number(e.target.value) };
              setFilters(newFilters);
              onFilterChange?.(newFilters);
            }}
          />
          <span>{filters.minUsers}</span>
        </div>
      </div>
    </div>
  );
};
```

### 4. 时间轴控制 (components/TimelineControl.tsx)
```typescript
interface TimelineControlProps {
  onTimeChange?: (timestamp: number) => void;
  timeRange?: [number, number];
  isPlaying?: boolean;
}

export const TimelineControl: React.FC<TimelineControlProps> = ({
  onTimeChange,
  timeRange = [Date.now() - 24 * 60 * 60 * 1000, Date.now()],
  isPlaying = false
}) => {
  const [currentTime, setCurrentTime] = useState(timeRange[1]);
  const [playing, setPlaying] = useState(isPlaying);
  const intervalRef = useRef<NodeJS.Timeout>();

  const handlePlay = () => {
    if (playing) {
      clearInterval(intervalRef.current);
      setPlaying(false);
    } else {
      setPlaying(true);
      intervalRef.current = setInterval(() => {
        setCurrentTime(prev => {
          const next = prev + 60000; // 每秒前进1分钟
          if (next > timeRange[1]) {
            setPlaying(false);
            clearInterval(intervalRef.current);
            return timeRange[0];
          }
          onTimeChange?.(next);
          return next;
        });
      }, 100);
    }
  };

  return (
    <div className="timeline-control">
      <button className="play-button" onClick={handlePlay}>
        {playing ? '⏸️' : '▶️'}
      </button>

      <div className="timeline-slider">
        <input
          type="range"
          min={timeRange[0]}
          max={timeRange[1]}
          value={currentTime}
          onChange={(e) => {
            const time = Number(e.target.value);
            setCurrentTime(time);
            onTimeChange?.(time);
          }}
        />
      </div>

      <div className="time-display">
        {new Date(currentTime).toLocaleString('zh-CN')}
      </div>
    </div>
  );
};
```

### 5. 样式定义 (styles/WorldMap.scss)
```scss
.world-map-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: var(--bg-primary);
  overflow: hidden;

  .world-map-chart {
    width: 100%;
    height: 100%;
  }

  .data-panel {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 300px;
    background: rgba(30, 41, 59, 0.9);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 20px;
    color: var(--text-primary);
    border: 1px solid var(--border-color);

    .stats-summary {
      margin-bottom: 20px;

      h3 {
        margin: 0 0 15px 0;
        font-size: 18px;
        font-weight: 600;
      }

      .stat-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;

        .stat-label {
          color: var(--text-secondary);
        }

        .stat-value {
          font-weight: 600;
          color: var(--active-color);
        }
      }
    }

    .top-countries {
      margin-bottom: 20px;

      h4 {
        margin: 0 0 10px 0;
        font-size: 14px;
        color: var(--text-secondary);
      }

      .country-item {
        display: flex;
        align-items: center;
        padding: 6px 0;
        border-bottom: 1px solid var(--border-color);

        .rank {
          width: 30px;
          font-size: 12px;
          color: var(--text-secondary);
        }

        .country-name {
          flex: 1;
          font-size: 13px;
        }

        .user-count {
          font-size: 12px;
          font-weight: 600;
          color: var(--active-color);
        }
      }
    }
  }

  .timeline-control {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(30, 41, 59, 0.9);
    backdrop-filter: blur(10px);
    padding: 15px 25px;
    border-radius: 25px;
    border: 1px solid var(--border-color);

    .play-button {
      background: var(--active-color);
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 15px rgba(251, 191, 36, 0.4);
      }
    }

    .timeline-slider {
      width: 300px;

      input[type="range"] {
        width: 100%;
        height: 4px;
        background: var(--border-color);
        border-radius: 2px;
        outline: none;

        &::-webkit-slider-thumb {
          appearance: none;
          width: 16px;
          height: 16px;
          background: var(--active-color);
          border-radius: 50%;
          cursor: pointer;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
        }
      }
    }

    .time-display {
      font-size: 12px;
      color: var(--text-secondary);
      min-width: 120px;
      text-align: center;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .world-map-container {
    .data-panel {
      width: 250px;
      top: 10px;
      right: 10px;
      padding: 15px;
    }

    .timeline-control {
      bottom: 20px;
      padding: 10px 15px;

      .timeline-slider {
        width: 200px;
      }
    }
  }
}

// 地图提示框样式
.map-tooltip {
  background: rgba(15, 23, 42, 0.95);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 12px;
  color: var(--text-primary);
  font-size: 13px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);

  h3 {
    margin: 0 0 8px 0;
    font-size: 16px;
    color: var(--active-color);
  }

  p {
    margin: 4px 0;
    color: var(--text-secondary);
  }
}
```

## 部署和集成指南

### 1. 项目初始化
```bash
# 创建项目
npx create-react-app world-map-viz --template typescript
cd world-map-viz

# 安装依赖
npm install echarts @types/echarts
npm install -D @types/node sass

# 启动开发服务器
npm start
```

### 2. 环境配置
```typescript
// config/environment.ts
export const config = {
  development: {
    apiBaseUrl: 'http://localhost:3001',
    wsUrl: 'ws://localhost:3001/realtime'
  },
  production: {
    apiBaseUrl: 'https://api.yourapp.com',
    wsUrl: 'wss://api.yourapp.com/realtime'
  }
};
```

### 3. API 集成示例
```typescript
// services/userDataService.ts
export class UserDataService {
  private baseUrl: string;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl;
  }

  async getCurrentUserDistribution(): Promise<UserDistributionData[]> {
    const response = await fetch(`${this.baseUrl}/users/distribution`);
    return response.json();
  }

  async getHistoricalData(timeRange: [number, number]): Promise<UserDistributionData[]> {
    const response = await fetch(
      `${this.baseUrl}/users/historical?start=${timeRange[0]}&end=${timeRange[1]}`
    );
    return response.json();
  }

  subscribeToRealTimeUpdates(callback: (data: UserDistributionData[]) => void): () => void {
    const ws = new WebSocket(`${this.baseUrl.replace('http', 'ws')}/realtime`);

    ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      callback(data);
    };

    return () => ws.close();
  }
}
```

这份完整的方案文档提供了从概念设计到具体实现的全方位指导，可以直接用作AI开发的完美提示词和技术参考。
